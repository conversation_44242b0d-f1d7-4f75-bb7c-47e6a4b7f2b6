@model Web_DATN.ViewModels.LinhKienFullViewModel
@{
    ViewData["Title"] = "Thêm linh kiện";
}

<h2>Thêm mớ<PERSON></h2>

<form asp-action="Create" method="post" asp-controller="LinhKien">
    <div class="mb-3">
        <label asp-for="TenLinhKien" class="form-label"></label>
        <input asp-for="TenLinhKien" class="form-control" />
        <span asp-validation-for="TenLinhKien" class="text-danger"></span>
    </div>

    <div class="mb-3">
        <label asp-for="DanhMucId" class="form-label">Danh mục</label>
        <select asp-for="DanhMucId" asp-items="Model.DanhMucs" class="form-control" id="danhMucSelect">
            <option value="">-- <PERSON><PERSON><PERSON> da<PERSON> mụ<PERSON> --</option>
        </select>
        <span asp-validation-for="DanhMucId" class="text-danger"></span>
    </div>

    <div class="mb-3">
        <label asp-for="Gia" class="form-label"></label>
        <input asp-for="Gia" class="form-control" />
        <span asp-validation-for="Gia" class="text-danger"></span>
    </div>

    <div class="mb-3">
        <label asp-for="MoTa" class="form-label"></label>
        <textarea asp-for="MoTa" class="form-control"></textarea>
    </div>

    <div class="form-check mb-3">
        <input asp-for="TrangThai" class="form-check-input" />
        <label class="form-check-label" asp-for="TrangThai">Hiển thị</label>
    </div>

    <h4>Thuộc tính linh kiện</h4>
    <div id="thuocTinhList"></div>

    <div class="mt-3">
        <button type="submit" class="btn btn-primary">Lưu</button>
        <a asp-action="Index" class="btn btn-secondary ms-2">Quay lại</a>
    </div>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        document.getElementById("danhMucSelect").addEventListener("change", async function () {
            const danhMucId = this.value;
            const container = document.getElementById("thuocTinhList");
            container.innerHTML = "";

            if (!danhMucId) return;

            try {
                const response = await fetch(`/LinhKien/GetThuocTinhsTheoDanhMuc?danhMucId=${danhMucId}`);
                if (!response.ok) throw new Error("Lỗi khi lấy thuộc tính");

                const data = await response.json();
                console.log("Data received:", data);

                // Kiểm tra cấu trúc dữ liệu trả về
                if (Array.isArray(data)) {
                    // Nếu data là array of thuộc tính
                    data.forEach((tt, index) => {
                        const html = `
                            <div class="border p-3 mb-2">
                                <input type="hidden" name="ThuocTinhs[${index}].ThuocTinhId" value="${tt.thuocTinh}" />
                                <label class="form-label">${tt.tenThuocTinh} ${tt.donVi ? '(' + tt.donVi + ')' : ''}</label>
                                <input name="ThuocTinhs[${index}].GiaTri" class="form-control mb-2" placeholder="Nhập giá trị" />
                                <input name="ThuocTinhs[${index}].MoTa" class="form-control mb-2" placeholder="Mô tả" />
                                <div class="form-check">
                                    <input type="hidden" name="ThuocTinhs[${index}].TrangThai" value="false" />
                                    <input type="checkbox" name="ThuocTinhs[${index}].TrangThai" value="true" class="form-check-input" checked />
                                    <label class="form-check-label">Hiển thị</label>
                                </div>
                            </div>`;
                        container.insertAdjacentHTML('beforeend', html);
                    });
                } else {
                    // Nếu data là object đơn lẻ
                    const html = `
                        <div class="border p-3 mb-2">
                            <input type="hidden" name="ThuocTinhs[0].ThuocTinhId" value="${data.thuocTinh}" />
                            <label class="form-label">${data.tenThuocTinh} ${data.donVi ? '(' + data.donVi + ')' : ''}</label>
                            <input name="ThuocTinhs[0].GiaTri" class="form-control mb-2" placeholder="Nhập giá trị" />
                            <input name="ThuocTinhs[0].MoTa" class="form-control mb-2" placeholder="Mô tả" />
                            <div class="form-check">
                                <input type="hidden" name="ThuocTinhs[0].TrangThai" value="false" />
                                <input type="checkbox" name="ThuocTinhs[0].TrangThai" value="true" class="form-check-input" checked />
                                <label class="form-check-label">Hiển thị</label>
                            </div>
                        </div>`;
                    container.insertAdjacentHTML('beforeend', html);
                }
            } catch (err) {
                console.error("Lỗi khi tải thuộc tính:", err);
            }
        });
    </script>
}
