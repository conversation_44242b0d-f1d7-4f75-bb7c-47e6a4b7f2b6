@model F4_API.Models.LinhKien

<div class="col-md-4">
    <a asp-action="Details" asp-controller="LinhKien" asp-route-id="@Model.LkId" class="product-link">
        <div class="card mb-4 shadow-sm product-card">
            <div class="product-img" style="background-image: url('@Url.Content(Model.ChiTiets != null && Model.ChiTiets.Any() && Model.ChiTiets.First().HinhAnhs.Any() ? "~/" + Model.ChiTiets.First().HinhAnhs.First().DuongDan : "~/img/sanpham/default.png")');">
            </div>
            <div class="card-body">
                <h5 class="card-title">@Model.TenLinhKien</h5>
                @{
                    var gia = Model.Gia ?? 0;
                }
                <p class="card-text">Giá: @gia.ToString("N0") VNĐ</p>
            </div>
        </div>
    </a>
</div>

<style>
    .product-link {
        text-decoration: none;
        color: inherit;
    }

    .product-card {
        border-radius: 10px;
        transition: transform 0.3s ease-in-out;
        overflow: hidden;
        cursor: pointer;
        box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
    }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0px 6px 15px rgba(0, 0, 0, 0.3);
        }

    .product-img {
        width: 100%;
        height: 300px;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        border-radius: 10px 10px 0 0;
    }

    .card-body {
        text-align: center;
        padding: 15px;
    }

    .card-text {
        font-size: 1rem;
        color: #ff6600;
        font-weight: bold;
    }
</style>
