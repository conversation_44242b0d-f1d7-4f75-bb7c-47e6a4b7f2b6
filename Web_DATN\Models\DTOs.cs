using System.ComponentModel.DataAnnotations;

namespace Web_DATN.Models
{
    public class LinhKienDTO
    {
        [Required]
        [StringLength(200)]
        public string TenLinh<PERSON>ien { get; set; } = string.Empty;
        
        public Guid DanhMucId { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal Gia { get; set; }
        
        public string? MoTa { get; set; }
        
        public bool TrangThai { get; set; } = true;
        
        public List<LinhKienChiTietsDTO>? linhKienCTs { get; set; }
    }
    
    public class LinhKienChiTietsDTO
    {
        public Guid? LkctId { get; set; }
        public Guid? LkId { get; set; }
        public Guid? ThuocTinhId { get; set; }
        public Guid? ThuongHieuId { get; set; }
        public Guid? HinhAnhId { get; set; }
        public string? GiaTri { get; set; }
        public string? MoTa { get; set; }
        public bool? TrangThai { get; set; } = true;
    }
    
    public class DanhMuc_LinhKien_ThuocTinh
    {
        public Guid DanhMucId { get; set; }
        public Guid ThuocTinhId { get; set; }
        public bool TrangThai { get; set; } = true;
        
        // Navigation properties
        public DanhMuc? DanhMuc { get; set; }
        public ThuocTinh? ThuocTinh { get; set; }
    }
    
    public class KhachHang
    {
        public Guid KhachHangId { get; set; }
        public string TenKhachHang { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string? SoDienThoai { get; set; }
        public DateTime? NgaySinh { get; set; }
        public string? GioiTinh { get; set; }
        public bool TrangThai { get; set; } = true;
    }

    public class NhanVien
    {
        public Guid NhanVienId { get; set; }
        public string TenNhanVien { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string? SoDienThoai { get; set; }
        public DateTime? NgaySinh { get; set; }
        public string? GioiTinh { get; set; }
        public string? DiaChi { get; set; }
        public Guid? ChucVuId { get; set; }
        public Guid? TaiKhoanId { get; set; }
        public bool TrangThai { get; set; } = true;

        // Navigation properties
        public ChucVu? ChucVu { get; set; }
        public TaiKhoan? TaiKhoan { get; set; }
    }

    public class ChucVu
    {
        public Guid ChucVuId { get; set; }
        public string TenChucVu { get; set; } = string.Empty;
        public string? MoTa { get; set; }
        public bool TrangThai { get; set; } = true;
    }

    public class TaiKhoan
    {
        public Guid TaiKhoanId { get; set; }
        public string TenDangNhap { get; set; } = string.Empty;
        public string MatKhau { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string VaiTro { get; set; } = string.Empty;
        public bool TrangThai { get; set; } = true;
    }
}
