@model List<F4_API.Models.LinhKien>
@{
    ViewData["Title"] = "Quản lý Sản phẩm";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Quản l<PERSON> phẩm (Linh kiện)</h2>
    <a asp-action="Create" class="btn btn-success" title="Thêm sản phẩm mới">
        <i class="fas fa-plus"></i> Thêm sản phẩm
    </a>
</div>

@if (ViewBag.Error != null)
{
    <div class="alert alert-danger">@ViewBag.Error</div>
}

@if (!Model.Any())
{
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i> Chưa có sản phẩm nào trong hệ thống.
    </div>
}
else
{
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0"><PERSON><PERSON> sách sản phẩm (@Model.Count() sản phẩm)</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>STT</th>
                            <th>Tên sản phẩm</th>
                            <th>Danh mục</th>
                            <th>Giá</th>
                            <th>Mô tả</th>
                            <th>Trạng thái</th>
                            <th class="text-center">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @{int stt = 1;}
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>@stt</td>
                                <td>
                                    <strong>@item.TenLinhKien</strong>
                                </td>
                                <td>
                                    <span class="badge bg-info">@(item.DanhMuc?.TenDanhMuc ?? "Chưa phân loại")</span>
                                </td>
                                <td>
                                    <span class="text-success fw-bold">@string.Format("{0:N0} đ", item.Gia)</span>
                                </td>
                                <td>
                                    @if (string.IsNullOrEmpty(item.MoTa))
                                    {
                                        <em class="text-muted">Không có mô tả</em>
                                    }
                                    else
                                    {
                                        <span title="@item.MoTa">
                                            @(item.MoTa.Length > 50 ? item.MoTa.Substring(0, 50) + "..." : item.MoTa)
                                        </span>
                                    }
                                </td>
                                <td>
                                    @if (item.TrangThai == true)
                                    {
                                        <span class="badge bg-success">
                                            <i class="fas fa-eye"></i> Hiển thị
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-eye-slash"></i> Ẩn
                                        </span>
                                    }
                                </td>
                                <td class="text-center">
                                    <div class="btn-group" role="group">
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Xem chi tiết">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-warning" title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" title="Xóa" onclick="confirmDelete('@item.LkId')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            stt++;
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
}

@section Scripts {
    <script>
        function confirmDelete(id) {
            if (confirm('Bạn có chắc chắn muốn xóa sản phẩm này?')) {
                // TODO: Implement delete functionality
                console.log('Delete product with ID:', id);
            }
        }
    </script>
}
