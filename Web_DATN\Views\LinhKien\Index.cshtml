@model List<F4_API.Models.LinhKien>
@{
    ViewData["Title"] = "Danh sách linh kiện";
}

<h2><PERSON><PERSON> sách <PERSON></h2>

<a asp-action="Create" class="btn btn-success mb-3">+ Thê<PERSON> linh kiện</a>

@if (!Model.Any())
{
    <div class="alert alert-info">Chưa có linh kiện nào.</div>
}
else
{
    <table class="table table-bordered table-striped">
        <thead>
            <tr>
                <th>Tên linh kiện</th>
                <th><PERSON>h mục</th>
                <th>Gi<PERSON></th>
                <th><PERSON>ô tả</th>
                <th>Trạng thái</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model)
            {
                <tr>
                    <td>@item.TenLinhKien</td>
                    <td>@item.DanhMuc?.TenDanhMuc</td>
                    <td>@string.Format("{0:N0} đ", item.Gia)</td>
                    <td>@item.MoTa</td>
                    <td>
                        @if (item.TrangThai == true)
                        {
                            <span class="badge bg-success">Hiển thị</span>
                        }
                        else
                        {
                            <span class="badge bg-secondary">Ẩn</span>
                        }
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
