﻿@model List<F4_API.Models.LinhKien>
@{
    ViewData["Title"] = "Danh sách linh kiện";
}

<div class="d-flex justify-content-between align-items-center mb-3">
    <h2><PERSON><PERSON> s<PERSON>ch <PERSON></h2>
    <a asp-action="Create" class="btn btn-success" title="Thêm linh kiện mới">
        <i class="fas fa-plus"></i>
    </a>
</div>

@if (!Model.Any())
{
    <div class="alert alert-info">Chưa có linh kiện nào.</div>
}
else
{
    <table class="table table-bordered table-striped">
        <thead>
            <tr>
                <th>Tên linh kiện</th>
                <th>Danh mục</th>
                <th>Giá</th>
                <th>Mô tả</th>
                <th>Trạng thái</th>
                <th>Thao tác</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model)
            {
                <tr>
                    <td>@item.TenLinhKien</td>
                    <td>@item.DanhMuc?.TenDanhMuc</td>
                    <td>@string.Format("{0:N0} đ", item.Gia)</td>
                    <td>@(string.IsNullOrEmpty(item.MoTa) ? "Không có mô tả" : (item.MoTa.Length > 50 ? item.MoTa.Substring(0, 50) + "..." : item.MoTa))</td>
                    <td>
                        @if (item.TrangThai == true)
                        {
                            <span class="badge bg-success">Hiển thị</span>
                        }
                        else
                        {
                            <span class="badge bg-secondary">Ẩn</span>
                        }
                    </td>
                    <td>
                        <a href="#" class="btn btn-sm btn-outline-primary" title="Xem chi tiết">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="#" class="btn btn-sm btn-outline-warning" title="Chỉnh sửa">
                            <i class="fas fa-edit"></i>
                        </a>
                        <a href="#" class="btn btn-sm btn-outline-danger" title="Xóa">
                            <i class="fas fa-trash"></i>
                        </a>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
